import * as React from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Tooltip from "@/components/genesis/atoms/tooltip/Tooltip";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import type { Table, Row } from "@tanstack/react-table";
import { Loader } from "lucide-react";
import IconButton from "../../molecules/iconButton";
import Divider from "../../atoms/divider/divider";
import { IcClose, IcMoveToTop, IcMoveToBotttom, IcMoreHorizontal } from "@/assets/icon-components";
import SelectorDropdown from "../../organisms/selectorDropdown";

interface DataTableActionBarProps<TData> extends React.HTMLAttributes<HTMLDivElement> {
  table: Table<TData>;
  /** @deprecated container prop is no longer used as action bar is positioned relative to table */
  container?: Element | DocumentFragment | null;
}

export function DataTableActionBar<TData>({
  table,
  container: containerProp, // Deprecated but kept for backward compatibility
  children,
  className,
  ...props
}: DataTableActionBarProps<TData>) {
  const selectedCount = Object.keys(table.getState().rowSelection).length;

  const visible = selectedCount > 0;
  if (!visible) return null;

  return (
    <div
      role="toolbar"
      aria-orientation="horizontal"
      className={cn(
        // Position relative to table container instead of viewport
        "absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full",
        // Styling
        "h-gd-48 z-50 flex w-fit flex-wrap items-center justify-center gap-gd-16",
        "rounded-gd-12 border bg-[var(--gd-neutral-black)] py-gd-8 px-gd-16",
        "text-foreground shadow-lg color-[white]",
        // Margin for spacing from table
        "mt-gd-8",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

interface DataTableActionBarActionProps extends React.ComponentProps<typeof Button> {
  tooltip?: string;
  isPending?: boolean;
}

export function DataTableActionBarAction({
  size = "sm",
  tooltip,
  isPending,
  disabled,
  className,
  children,
  ...props
}: DataTableActionBarActionProps) {
  const trigger = (
    <Button
      variant="ghost"
      size={size}
      disabled={disabled || isPending}
      className={cn(
        "flex items-center gap-gd-4 text-white hover:text-white hover:bg-color-gd-neutral-grey-90 px-0",
        className
      )}
      {...props}
    >
      {isPending ? <Loader className="animate-spin" /> : children}
    </Button>
  );

  if (!tooltip) return trigger;

  return (
    <Tooltip content={tooltip} sideOffset={15} delayDuration={800}>
      {trigger}
    </Tooltip>
  );
}

interface DataTableActionBarSelectionProps<TData> {
  table: Table<TData>;
}

export function DataTableActionBarSelection<TData>({
  table,
}: DataTableActionBarSelectionProps<TData>) {
  const onClear = React.useCallback((): void => {
    table.toggleAllRowsSelected(false);
  }, [table]);

  // number of rows selected across _all_ pages:
  const totalSelected: number = Object.keys(table.getState().rowSelection).length;
  const totalRows: number = table.getFilteredRowModel()?.rows?.length ?? 0;

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      if (event.key === "Escape" && totalSelected > 0) {
        onClear();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [onClear, totalSelected]);

  return (
    <div className="flex h-7 items-center gap-gd-16">
      <div className="flex items-center gap-gd-8">
        <Tooltip
          content={
            <span className="inline-flex gap-gd-8 items-center">
              Clear selection <kbd className="border-[1px] px-gd-4 py-gd-2 rounded-md">Esc</kbd>
            </span>
          }
          sideOffset={15}
          containerStyle={{ display: "flex" }}
          delayDuration={800}
        >
          <IconButton
            type="tertiary"
            size="md"
            classNames={{
              icon: "flex items-center",
              button: "flex items-center",
            }}
            icon={<IcClose />}
            onClick={onClear}
            inverse
          />
        </Tooltip>

        <span className="whitespace-nowrap text-en-desktop-body-m text-white">
          {totalSelected} of {totalRows} selected
        </span>
      </div>

      <Divider
        orientation="vertical"
        lineClassName="border-color-neutral-grey-70-inverse"
        className="h-[24px]"
        subtle
      />
    </div>
  );
}

interface OverflowItem {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

export function DataTableActionBarOverflow({ items }: { items: OverflowItem[] }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <IconButton type="tertiary" size="md" icon={<IcMoreHorizontal />} inverse />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {items.map(item => (
          <DropdownMenuItem key={item.label} onClick={item.onClick} disabled={item.disabled}>
            {item.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface ActionBarRowMovementControlsProps<TData extends object> {
  /**
   * TanStack React Table instance managing the rows and selection
   */
  table: Table<TData>;
  /**
   * Additional class name(s) for styling
   */
  className?: string;
}

export function ActionBarRowMovementControls<TData extends object>({
  table,
  className = "",
}: ActionBarRowMovementControlsProps<TData>) {
  /**
   * Handle moving selected rows up one position
   */
  const handleMoveUp = React.useCallback(() => {
    const selection = Object.keys(table.getState().rowSelection);
    if (selection.length === 0) return;
    table.getSelectedRowModel().rows.forEach((row: Row<TData>) => {
      if (row.index > 0) {
        console.log("Moving row up:", row.original);
      }
    });
  }, [table]);

  /**
   * Handle moving selected rows down one position
   */
  const handleMoveDown = React.useCallback(() => {
    const selection = Object.keys(table.getState().rowSelection);
    if (selection.length === 0) return;
    const total = table.getRowModel().rows.length;
    table.getSelectedRowModel().rows.forEach((row: Row<TData>) => {
      if (row.index < total - 1) {
        console.log("Moving row down:", row.original);
      }
    });
  }, [table]);

  /**
   * Handle moving selected rows to top or bottom via dropdown
   */
  const handleDropdownChange = React.useCallback((value: string) => {
    if (value === "0") {
      console.log("Moving rows to top");
    } else if (value === "1") {
      console.log("Moving rows to bottom");
    }
  }, []);

  return (
    <div className={`flex items-center gap-gd-16 ${className}`.trim()}>
      <Tooltip content="Move Up" sideOffset={15} delayDuration={800}>
        <IconButton
          type="tertiary"
          size="md"
          icon={<IcMoveToTop />}
          onClick={handleMoveUp}
          inverse
        />
      </Tooltip>

      <Tooltip content="Move Down" sideOffset={15} delayDuration={800}>
        <IconButton
          type="tertiary"
          size="md"
          icon={<IcMoveToBotttom />}
          onClick={handleMoveDown}
          inverse
        />
      </Tooltip>

      <SelectorDropdown
        size="md"
        options={[
          <div key="0" className="text-sm flex items-center gap-gd-8">
            <span>Move to Top</span>
          </div>,
          <div key="1" className="text-sm flex items-center gap-gd-8">
            <span>Move to Bottom</span>
          </div>,
        ]}
        onChange={handleDropdownChange}
        placeholder="Move To Position"
        className="text-white bg-transparent focus-visible:ring-0 hover:bg-transparent hover:text-white [&>svg]:text-white [&>svg]:hover:text-white [&>svg]:active:text-white [&>svg]:focus-visible:text-white"
      />

      <Divider
        orientation="vertical"
        lineClassName="border-color-neutral-grey-70-inverse"
        className="h-[24px]"
        subtle
      />
    </div>
  );
}
