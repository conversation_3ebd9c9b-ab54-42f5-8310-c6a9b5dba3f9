import React, { useState } from "react";
import { DataTable, useDataTable } from "./index";
import { DataTableActionBar, DataTableActionBarAction, DataTableActionBarSelection } from "./data-table-action-bar";
import { ColumnDef } from "@tanstack/react-table";

interface TestUser {
  id: number;
  name: string;
  email: string;
  role: string;
}

const testData: TestUser[] = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", role: "Admin" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", role: "Editor" },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", role: "User" },
  { id: 5, name: "<PERSON>", email: "<EMAIL>", role: "Admin" },
];

const columns: ColumnDef<TestUser>[] = [
  { accessorKey: "id", header: "ID", size: 60 },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "email", header: "Email" },
  { accessorKey: "role", header: "Role" },
];

function TestTable({ title, data }: { title: string; data: TestUser[] }) {
  const { table, originalColumns } = useDataTable({
    data,
    columns,
    pageCount: Math.ceil(data.length / 10),
    enableRowSelection: true,
  });

  const handleDelete = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    console.log(`${title} - Delete:`, selectedRows.map(row => row.original));
  };

  const handleExport = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    console.log(`${title} - Export:`, selectedRows.map(row => row.original));
  };

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <DataTable 
        table={table} 
        originalColumns={originalColumns}
        showOuterBorder={true}
      >
        <DataTableActionBar table={table}>
          <DataTableActionBarSelection table={table} />
          <DataTableActionBarAction
            tooltip="Delete selected users"
            onClick={handleDelete}
          >
            <span>🗑️</span>
            <span>Delete</span>
          </DataTableActionBarAction>
          <DataTableActionBarAction
            tooltip="Export selected users"
            onClick={handleExport}
          >
            <span>📤</span>
            <span>Export</span>
          </DataTableActionBarAction>
        </DataTableActionBar>
      </DataTable>
    </div>
  );
}

/**
 * Test component to demonstrate the fixed DataTableActionBar positioning.
 * 
 * This test shows:
 * 1. Multiple tables on the same page
 * 2. Each table has its own action bar that doesn't interfere with others
 * 3. Action bars are positioned relative to their table containers
 * 4. Action bars move with their tables when scrolling
 * 
 * Before the fix:
 * - All action bars would overlap at the bottom of the viewport
 * - Action bars would stay fixed while tables scrolled
 * 
 * After the fix:
 * - Each action bar is positioned relative to its table
 * - Action bars move with their tables
 * - No overlap between multiple table action bars
 */
export default function TestActionBarPositioning() {
  return (
    <div className="p-6 space-y-8 max-h-screen overflow-y-auto">
      <div>
        <h1 className="text-2xl font-bold mb-6">DataTable Action Bar Positioning Test</h1>
        <p className="text-gray-600 mb-8">
          This test demonstrates the fixed positioning of DataTableActionBar components. 
          Select rows in any table to see the action bar appear positioned relative to that specific table.
        </p>
      </div>

      <TestTable 
        title="Table 1 - Users" 
        data={testData.slice(0, 3)} 
      />

      <TestTable 
        title="Table 2 - Admins" 
        data={testData.filter(user => user.role === "Admin")} 
      />

      <TestTable 
        title="Table 3 - All Users" 
        data={testData} 
      />

      <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
        <p className="text-gray-500">
          Scroll area to test action bar positioning during scroll
        </p>
      </div>

      <TestTable 
        title="Table 4 - Bottom Table" 
        data={testData.slice(2)} 
      />

      <div className="h-64 bg-blue-50 rounded-lg flex items-center justify-center">
        <p className="text-blue-600">
          End of test area - action bars should stay with their respective tables
        </p>
      </div>
    </div>
  );
}
