import { useEffect, useState } from "react";
import "./App.css";
import { RightPanel } from "./components/genesis/organisms/right-panel/right-panel";

import {
  IcSettings,
  IcCard,
  IcProfile,
  IcUser,
  IcConfirm,
  IcChevronRight,
  IcHome,
  IcFolder,
  IcDocument,
  IcCloudSynced,
} from "./assets/icon-components";
import { Avatar as AvatarShadCn, AvatarFallback, AvatarImage } from "./components/ui/avatar";
import Avatar from "./components/genesis/atoms/avatar/avatar";
import { Button } from "./components/genesis/atoms/button/button";
import useThemeMode from "./hooks/use-themeMode";
import { cn } from "./lib/utils";
import { setAppTheme } from "./hooks/listen-theme-change";
import { RadioButtons } from "./components/genesis/atoms/radio-button/radio-buttons";
import Tabs from "./components/genesis/atoms/tabs/Tabs";
import Tooltip from "./components/genesis/atoms/tooltip/Tooltip";
import FileUploadParent from "./components/genesis/molecules/file-uploader/file-upload-parent";
// import CounterBadge from "./components/genesis/atoms/counterBadge";
import {
  BottomSheet,
  Checkbox,
  Divider,
  Input,
  Link,
  Modal,
  ProgressBar,
  Scrollbar,
  SwipeButton,
  Toggle,
} from "./components/genesis";
import Accordion from "./components/genesis/molecules/accordian";
// import ProgressBar from "./components/genesis/molecules/feedback-progress";
import IconButton from "./components/genesis/molecules/iconButton";
import Popover from "./components/genesis/molecules/popover/popover";
import SelectorDropdown from "./components/genesis/organisms/selectorDropdown";
import Breadcrumb from "./components/genesis/atoms/breadcrumb/breadcrumb";
import { Banner } from "./components/genesis/molecules/notification-banner";
import { ExampleTable_fetch } from "./components/genesis/organisms/data-table/example-table-fetch";
import { Text } from "./components/genesis/atoms/text/text";
import FileViewer from "./components/genesis/molecules/file-viewer";
import { getDocument, GlobalWorkerOptions } from "pdfjs-dist/legacy/build/pdf.mjs";
import EditableTableDemo from "./components/genesis/organisms/data-table/editable-table-demo";
import { TestPaddingSimple } from "./components/genesis/organisms/data-table/test-padding-simple";
import { TestLayoutShiftFixes } from "./components/genesis/organisms/data-table/test-layout-shift-fixes";
import TestActionBarPositioning from "./components/genesis/organisms/data-table/test-action-bar-positioning";

// Configure the PDF.js worker
GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/legacy/build/pdf.worker.min.mjs",
  import.meta.url
).href;

// GlobalWorkerOptions.workerSrc =
//   "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";

// GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";

const option3 = [
  {
    id: 1,
    label: "Profile",
    icon: (
      <AvatarShadCn className="w-6 h-6">
        <AvatarImage className="" src="https://github.com/shadcn.png" alt="@shadcn" />
        <AvatarFallback>CN</AvatarFallback>
      </AvatarShadCn>
    ),
    helpText: "This is your profile.",
    type: "Text",
  },
  {
    id: 2,
    label: "Settings",
    icon: <IcSettings />,
    helpText: "This is your settings.",
    type: "Text",
  },
  {
    id: 3,
    label: "Billing",
    icon: <IcCard />,
    helpText: "This is your billing.",
    type: "Text",
    groupId: 1,
  },
  {
    id: 4,
    label: "Team",
    icon: (
      <AvatarShadCn className="w-6 h-6">
        <AvatarFallback>CN</AvatarFallback>
      </AvatarShadCn>
    ),
    helpText: "This is your team.",
    type: "Text",
    groupId: 1,
  },
  {
    id: 5,
    label: "Profile 2",
    icon: <IcProfile />,
    helpText: "This is your profile.",
    type: "Text",
    groupId: 1,
  },
  {
    id: 6,
    label: "Settings 2",
    icon: <IcSettings />,
    helpText: "This is your settings.",
    type: "Text",
    groupId: 1,
  },
  {
    id: 7,
    label: "Billing 2",
    icon: <IcCard />,
    helpText: "This is your billing.",
    type: "Text",
    groupId: 1,
    disabled: true,
  },
  {
    id: 8,
    label: "Team 2",
    icon: <IcUser />,
    helpText: "This is your team.",
    type: "Text",
    groupId: 2,
  },
  {
    id: 9,
    label: "Profile 3",
    icon: <IcProfile />,
    helpText: "This is your profile.",
    type: "Text",
    groupId: 2,
    disabled: true,
  },
  {
    id: 10,
    label: "Settings 3",
    icon: <IcSettings />,
    helpText: "This is your settings.",
    type: "Text",
    groupId: 3,
  },
  {
    id: 11,
    label: "Billing 3",
    icon: <IcCard />,
    helpText: 'This is your billing."',
    type: "Text",
    groupId: 3,
  },
  {
    id: 12,
    label: "Team 3",
    icon: <IcUser />,
    helpText: "This is your team.",
    type: "Text",
    groupId: 3,
  },
];

const groups = [
  {
    id: 1,
    label: "This is Group Label",
  },
  {
    id: 2,
    label: "This is Group Label 2",
  },
  {
    id: 3,
    label: "",
  },
];

function App() {
  const longPathItems = [
    {
      label: "Home",
      href: "#",
      icon: (
        <IcHome className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
      isHome: true,
    },
    {
      label: "Products",
      href: "#",
      icon: (
        <IcFolder className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
    },
    {
      label: "Electronics",
      href: "#",
      icon: (
        <IcFolder className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
    },
    {
      label: "Computers",
      href: "#",
      icon: (
        <IcFolder className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
    },
    {
      label: "Laptops",
      href: "#",
      icon: (
        <IcFolder className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
    },
    {
      label: "MacBook Pro",
      icon: (
        <IcDocument className="h-4 w-4 mr-1 text-color-neutral-grey-80 hover:text-color-primary-50 active:text-color-primary-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-color-primary-60" />
      ),
    },
  ];
  const breadcrumbItems = [
    { label: "B01", href: "#", isHome: true },
    { label: "B02", href: "#" },
    { label: "B03", href: "#" },
    { label: "B04", href: "#" },
    { label: "B05", href: "#" },
    { label: "B06", href: "#" },
    { label: "B07" }, // current page
  ];

  const customSeparator = <span className="mx-1 text-gray-500">/</span>;

  // Custom ellipsis component example
  const customEllipsis = <span className="text-gray-500">...</span>;

  // Click handler example
  const handleBreadcrumbClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    console.log("Breadcrumb link clicked", e.currentTarget.href);
  };

  // Ellipsis click handler example
  const handleEllipsisClick = () => {
    console.log("Ellipsis clicked - showing custom dropdown");
    // You could implement your own dropdown logic here
  };
  const [value, setValue] = useState([]);

  const [isDarkMode, setIsDarkMode] = useState(true);
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);

  const handleSelect = (option: any, currentSelctedItem: any) => {
    console.log("Selected:", option);
    setValue(option);
  };

  const handleSelectOptions = (option: any) => {
    console.log("Selected:", option);
    setSelectedOptions(option);
  };

  const handleAddOption = () => {};

  console.log("Rendering---");

  const themeMode = useThemeMode();

  useEffect(() => {
    document.body.classList.add("light", "falcon");
  }, []);

  const changeMode = (theme = "falcon", mode = "theme") => {
    setAppTheme(theme, mode);
  };

  console.log("theme mode changed in app ", themeMode);

  const tabItems = [
    {
      value: "tab1",
      label: "Tab 1",
      content: <div>Content for Tab 1</div>,
      icon: <IcSettings />,
    },
    {
      value: "tab2",
      label: "Tab 2",
      content: <div>Content for Tab 2</div>,
      icon: <IcCard />,
      tabCount: 5,
    },
    {
      value: "tab3",
      label: "Tab 3",
      content: <div>Content for Tab 3</div>,
      icon: <IcProfile />,
    },
  ];

  const [contextMenuTabs, setContextMenuTabs] = useState([...tabItems]);
  const [activeContextTab, setActiveContextTab] = useState("tab1");

  const handleTabRename = (value: string, newLabel: string) => {
    setContextMenuTabs(prevTabs =>
      prevTabs.map(tab => (tab.value === value ? { ...tab, label: newLabel } : tab))
    );
  };

  const handleTabDuplicate = (value: string) => {
    const tabToDuplicate = contextMenuTabs.find(tab => tab.value === value);
    if (tabToDuplicate) {
      const newTab = {
        ...tabToDuplicate,
        value: `${value}_copy_${Date.now()}`,
        label: `${tabToDuplicate.label} (Copy)`,
      };
      setContextMenuTabs(prevTabs => [...prevTabs, newTab]);
    }
  };

  const handleTabDelete = (value: string) => {
    if (contextMenuTabs.length <= 1) return; // Prevent deleting the last tab

    setContextMenuTabs(prevTabs => prevTabs.filter(tab => tab.value !== value));

    if (value === activeContextTab) {
      const remainingTabs = contextMenuTabs.filter(tab => tab.value !== value);
      if (remainingTabs.length > 0) {
        setActiveContextTab(remainingTabs[0].value);
      }
    }
  };

  const [openModal, setOpenModal] = useState(false);

  return (
    <div className="m-2 p-4">
      // Small size
      <Input size="s" disabled={true} label="Size s" placeholder="Size S" />
      // Medium size
      <Input size="m" disabled={true} label="Size m" placeholder="Size m" />
      // Large size
      <Input size="l" disabled={true} label="Size l" placeholder="Size L" />
      <div
        className={cn(
          `bg-color-primary-30`,
          `border-color-primary-80`,
          `border-[5px] text-6xl font-bold h-7`,
          "rounded-16 hover:bg-color-feedback-error-50"
        )}
      ></div>
      <div
        className={cn(
          `bg-color-primary-30`,
          `border-color-primary-80`,
          `border-[5px] text-6xl font-bold h-7`,
          `hover:bg-color-neutral-grey-80`,
          `rounded-32`
        )}
      ></div>
      // Input with Apply button
      <Input
        label="With Apply Button"
        placeholder="Type and click Apply"
        value={value}
        disabled={true}
        onValueChange={setValue}
        suffix={
          <Button
            disabled={true}
            kind="tertiary"
            onClick={() => {
              console.log("Sync Data button clicked");
            }}
          >
            Apply
          </Button>
        }
        showSuffixDivider={false}
      />
      // Input with Sync Data button
      <Input
        label="With Sync Button"
        placeholder="Click Sync Data"
        disabled={true}
        value={value}
        showSuffixDivider={false}
        onValueChange={setValue}
        suffix={
          <Button
            kind="tertiary"
            disabled={true}
            className="w-[100px] white-space-nowrap"
            onClick={() => {
              console.log("Sync Data button clicked");
            }}
          >
            <IcCloudSynced height={16} width={16} />
            Sync Data
          </Button>
        }
        showSuffixDivider={true}
      />
      <br />
      <br />
      <RadioButtons
        options={["abc", "def", "fgth"]}
        defaultVal={"abc"}
        // size={"s"}
        alertType="success"
      />
      <Input
        data-testid="input_margin_ll_perct"
        label={"CREATE_OTB.LABELS.LOWER_LIMIT"}
        onChange={async e => {}}
        disabled={false}
        required
        value={0}
        suffix="123"
        showSuffixDivider={true}
        type="text"
        size="m"
        placeholder=""
      />
      <br />
      <br />
      <Button onClick={() => changeMode("jarvis", "light")}>Change to Jarvis Light</Button>
      <Button onClick={() => changeMode("jarvis", "dark")}>Change to Jarvis Dark</Button>
      <br />
      <br />
      <Button onClick={() => changeMode("falcon", "light")}>Change to Falcon Light</Button>
      <Button onClick={() => changeMode("falcon", "dark")}>Change to Falcon Dark</Button>
      <br />
      <br />
      <Button onClick={() => changeMode("phoenix", "light")}>Change to Phoenix Light</Button>
      <Button onClick={() => changeMode("phoenix", "dark")}>Change to Phoenix Dark</Button>
      <br />
      <br />
      <div className="gap-gd-12">
        <Scrollbar maxHeight="100px">
          <div className="flex flex-row gap-gd-8 mt-gd-8">
            <Avatar />
            <Divider orientation="vertical" />
            {/* <CounterBadge /> */}
            <Divider orientation="vertical" />
            <SwipeButton
              icon={<IcChevronRight size="16px" />}
              confirmIcon={<IcConfirm size="16px" />}
            />
            <Divider orientation="vertical" />
            <Toggle />
          </div>
        </Scrollbar>
        <br />
        <br />
        <Accordion items={[{ label: "label", content: <span>Content</span>, value: "item-1" }]} />
        <br />
        <br />
        {/* <Checkbox alertType="error" size="l">
          Hello
        </Checkbox> */}
        <br />
        <br />
        <ProgressBar value={50} label="Progress Bar" />
        <br />
        <br />
        <div className="m-gd-12">
          <ProgressBar variant="circle" value={50} color="default" />
        </div>
        <br />
        <br />
        <IconButton icon={<IcConfirm size="16px" />} />
        <br />
        <br />
        <Link link="https://chatgpt.com/chat/2weqwdwc" subtle={false} />
        <br />
        <br />
        <Popover trigger={<Button>Open Popover</Button>} open={open} onOpenChange={setOpen}>
          Popover Content
        </Popover>
        <br />
        <br />
        <SelectorDropdown
          placeholder="Select an option"
          icon={<IcProfile />}
          options={[
            <div className="flex items-center gap-2">
              <IcConfirm />
              Apple
            </div>,
            <div className="flex items-center gap-2">
              <IcChevronRight />
              Banana
            </div>,
            <div className="flex items-center gap-2">
              <IcCard />
              Grapes
            </div>,
          ]}
        ></SelectorDropdown>
      </div>
      <br />
      <br />
      <Banner
        isSingleLine
        variant="success"
        description="djadkajdhakdhajdhawjdhajdhajkdhawdhakwjdhawkj"
      />
      <br />
      <br />
      <RightPanel title="Test Right panel" width="s" maxWidth="l" extension showBack></RightPanel>
      <br />
      <br />
      <br />
      <FileUploadParent />
      <br />
      <br />
      <Tabs defaultValue="tab1" items={tabItems} kind="default" size="l" />
      <br />
      <br />
      <Tabs defaultValue="tab1" items={tabItems} kind="subtab" size="s" />
      <br />
      <br />
      {/* Add tabs with context menu */}
      <div>
        <h3 className="mb-4 text-lg font-medium">Tabs with Context Menu</h3>
        <Tabs
          items={contextMenuTabs}
          value={activeContextTab}
          onValueChange={setActiveContextTab}
          kind="default"
          size="l"
          enableTabContextMenu
          onTabRename={handleTabRename}
          onTabDuplicate={handleTabDuplicate}
          onTabDelete={handleTabDelete}
        />
        <div className="mt-4 p-4 border rounded-md">
          <p>Current active tab: {activeContextTab}</p>
          <p>Try the dropdown menu on each tab to rename, duplicate, or delete tabs.</p>
        </div>
      </div>
      <br />
      <br />
      <div>
        <h3 className="mb-4 text-lg font-medium">Tooltip Examples</h3>
        <div className="flex items-center space-x-4">
          <Tooltip content="This is a simple tooltip">
            <Button>Hover Me</Button>
          </Tooltip>

          <Tooltip
            content={<div className="p-2">Tooltip with custom content and styling</div>}
            side="right"
            className="bg-color-primary-50 text-white"
            arrowClassName="fill-color-primary-50"
          >
            <Button>Right Side Custom Tooltip</Button>
          </Tooltip>

          <Tooltip content="Tooltip with delay" delayDuration={500} side="bottom">
            <Button>Delayed Tooltip</Button>
          </Tooltip>

          <Tooltip
            content="Tooltip content snkjfhjdskf hdsufh idsu hfiusdhfi usd h ifdsh oif sdo ifhfsu gufsgiufsh giush giusfh iugh sf iuhgiuf higudfh giudfhgiu dhfiug hdfiughiughisfuhiugdfh giudfh giudf hiugdfhiuhdfiuhdfiughdfiughiudf hgiu sdbfisdh fiushfius hifu shdifu shdi uhfsiud fhiusd hiusd hifusdfiusdhfiudshiuf hsdifh isduhf iudsh fiufsd hfiudshi uhsiufhdsiufhisud hiuds hiufs hdiufh sdiu fhdsiu hfiusdh iusdhfiuhsdiuf hsduifhsdiufh zcfb iusdhfiusd hfoisdh oifh dsoi fhdosif hoids hfoids hfoids hfoisdhfoih Tooltip content snkjfhjdskf hdsufh idsu hfiusdhfi usd h ifdsh oif sdo ifhfsu gufsgiufsh giush giusfh iugh sf iuhgiuf higudfh giudfhgiu dhfiug hdfiughiughisfuhiugdfh giudfh giudf hiugdfhiuhdfiuhdfiughdfiughiudf hgiu sdbfisdh fiushfius hifu shdifu shdi uhfsiud fhiusd hiusd hifusdfiusdhfiudshiuf hsdifh isduhf iudsh fiufsd hfiudshi uhsiufhdsiufhisud hiuds hiufs hdiufh sdiu fhdsiu hfiusdh iusdhfiuhsdiuf hsduifhsdiufh zcfb iusdhfiusd hfoisdh oifh dsoi fhdosif hoids hfoids hfoids hfoisdhfoih"
            side="bottom"
          >
            <Button>Tooltip with large text</Button>
          </Tooltip>

          <Tooltip
            content={
              <div className="flex items-center gap-gd-4 p-1">
                <IcSettings className="h-5 w-5" />
                <span>Tooltip with Icon</span>
              </div>
            }
          >
            <Button>Icon Tooltip</Button>
          </Tooltip>
        </div>
      </div>
      <div className="p-8 space-y-8">
        <div className="space-y-2">
          <div className="p-4 rounded-md">
            <Breadcrumb items={longPathItems} maxItems={6} />
          </div>
        </div>
        <div className="space-y-2">
          <div className="p-4 rounded-md">
            <Breadcrumb
              items={longPathItems}
              collapseAfter={2}
              maxItems={3}
              // onEllipsisClick={handleEllipsisClick}
            />
          </div>
        </div>
      </div>
      <br />
      <br />
      <div className="space-y-8 p-8">
        <div className="space-y-2">
          <h2 className="text-xl font-bold">Display Variants</h2>
          <Text variant="display-2xl">Display 2XL</Text>
          <Text variant="display-xl">Display XL</Text>
          <Text variant="display-l">Display L</Text>
          <Text variant="display-m">Display M</Text>
          <Text variant="display-s">Display S</Text>
          <Text variant="display-xs">Display XS</Text>
          <Text variant="display-2xs">Display 2XS</Text>
        </div>

        <div className="space-y-2">
          <h2 className="text-xl font-bold">Heading Variants</h2>
          <Text variant="heading-2xl">Heading 2XL</Text>
          <Text variant="heading-xl">Heading XL</Text>
          <Text variant="heading-l">Heading L</Text>
          <Text variant="heading-m">Heading M</Text>
        </div>

        <div className="space-y-2">
          <h2 className="text-xl font-bold">Body Variants</h2>
          <Text variant="body-2xl-prominent">Body 2XL Prominent</Text>
          <Text variant="body-xl-prominent">Body XL Prominent</Text>
          <Text variant="body-xl">Body XL</Text>
          <Text variant="body-l-prominent">Body L Prominent</Text>
          <Text variant="body-l">Body L</Text>
          <Text variant="body-m-prominent">Body M Prominent</Text>
          <Text variant="body-m">Body M</Text>
          <Text variant="body-s-prominent">Body S Prominent</Text>
          <Text variant="body-s">Body S</Text>
          <Text variant="body-xs-prominent">Body XS Prominent</Text>
          <Text variant="body-xs">Body XS</Text>
        </div>

        <div className="space-y-2">
          <h2 className="text-xl font-bold">Overriding HTML Elements</h2>
          <Text variant="display-l" as="div">
            This is a div with display-l styling
          </Text>
          <Text variant="body-m" as="h1">
            This is an h1 with body-m styling
          </Text>
          <Text variant="heading-l" as="p">
            This is a paragraph with heading-l styling
          </Text>
        </div>

        <div className="space-y-2">
          <h2 className="text-xl font-bold">Additional Styling</h2>
          <Text variant="heading-xl" className="text-blue-500 underline">
            Custom styled heading
          </Text>
          <Text variant="body-m-prominent" className="italic text-green-600">
            Custom styled body text
          </Text>
        </div>
      </div>
      {/* <div className="h-screen"> */}
      <h1>Data Table</h1>
      {/* <ExampleTable /> */}
      <ExampleTable_fetch />
      <EditableTableDemo />
      <TestLayoutShiftFixes />
      <TestPaddingSimple />
      <TestActionBarPositioning />
      {/* </div> */}
      <Modal open={openModal} setOpen={setOpenModal}>
        <p>
          It started as a quiet whisper in the walls. At first, Jacob thought it was the old pipes
          groaning under the weight of the crumbling house he had just moved into. But as the days
          passed, the whispers became clearer, forming words that were just barely distinguishable
          in the dead of night. "Jacob... let me out," the voice pleaded, echoing through the
          darkness of his bedroom. He told himself it was his imagination, the wind sneaking through
          the cracks, the house settling. But deep inside, he knew better. The previous owner had
          disappeared without a trace, leaving behind all his possessions. The locals avoided
          talking about the house or its former resident. One night, Jacob awoke to find all the
          doors in his house wide open, even though he distinctly remembered closing and locking
          them before bed. A trail of wet footprints led from his bedroom door to the basement
          entrance. The footprints weren't his. They were too small, like those of a child. With
          trembling hands, Jacob followed the trail down the creaking stairs into the darkness
          below. The whispers grew louder with each step. "Jacob... you found me... now let me in."
          In the corner of the basement, behind a recently disturbed section of wall, Jacob
          discovered a small, hidden room. Inside was a single chair facing the wall, and dozens of
          photographs pinned to every surface. Each photo showed the same thing: Jacob sleeping in
          his bed, taken from inside his room, dating back to the day he moved in. On the chair was
          a small, antique music box. As Jacob picked it up, it began to play a haunting melody. The
          whispers suddenly stopped. In the silence that followed, Jacob heard a soft giggle
          directly behind him. Before he could turn around, cold, small hands covered his eyes. "My
          turn to hide," the voice whispered in his ear. When the neighbors reported strange noises
          coming from the old house, the police found it empty once again. The only new addition was
          a fresh photograph on the basement wall: Jacob sitting in the chair, his eyes wide with
          terror, looking directly at the camera. The house is for sale again. They say it's a
          steal. The whispers grew louder with each step. "Jacob... you found me... now let me in."
          In the corner of the basement, behind a recently disturbed section of wall, Jacob
          discovered a small, hidden room. Inside was a single chair facing the wall, and dozens of
          photographs pinned to every surface. Each photo showed the same thing: Jacob sleeping in
          his bed, taken from inside his room, dating back to the day he moved in. On the chair was
          a small, antique music box. As Jacob picked it up, it began to play a haunting melody. The
          whispers suddenly stopped. In the silence that followed, Jacob heard a soft giggle
          directly behind him. Before he could turn around, cold, small hands covered his eyes. "My
          turn to hide," the voice whispered in his ear. When the neighbors reported strange noises
          coming from the old house, the police found it empty once again. The only new addition was
          a fresh photograph on the basement wall: Jacob sitting in the chair, his eyes wide with
          terror, looking directly at the camera. The house is for sale again. They say it's a
          steal.
        </p>
      </Modal>
      <br />
      <br />
      <FileViewer
        files={["https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"]}
        mode="pdf"
        headerTitle="PDF Viewer"
        buttonTitle="Open PDF"
        onDownload={file => {
          // Handle PDF download
          // window.open(file, "_blank");
          console.log("pdf url ", file);
        }}
      />
      <br />
      <br />
      <FileViewer
        files={[
          "https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d",
          "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
          "https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e",
          "https://images.unsplash.com/photo-1517841905240-472988babdf9",
          "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d",
          "https://images.unsplash.com/photo-1500648767791-00dcc994a43e",
          "https://images.unsplash.com/photo-1524504388940-b1c1722653e1",
        ]}
        // mode="pdf"
        headerTitle="Image Viewer"
        buttonTitle="Open Images"
        onDownload={file => {
          // Handle PDF download
          // window.open(file, "_blank");
          console.log("image url ", file);
        }}
      />
      <br />
      <br />
      <Button
        onClick={async () => {
          const pdf = await getDocument(
            "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
          ).promise;
          console.log("convertPdfToImages", pdf);
        }}
      >
        Convert PDF
      </Button>
    </div>
  );
}

export default App;
